<script setup>
import TopTitle from "@/views/components/TopTitle/index.vue";
import { Search, RefreshRight, Plus } from "@element-plus/icons-vue";
import { useUserStore } from "@/store/modules/user";
import { getUserListApi, resetUserPasswordApi } from "@/apis/user-management";

const userStore = useUserStore()

// 搜索表单数据
const searchForm = ref({
  userType: "", // 用户类型
  searchKeyword: "", // 手机号/姓名搜索
});

// 根据当前用户角色获取用户类型选项
const getUserTypeOptions = () => {
  const currentRoles = userStore.roles;

  // 省级用户：可以看到地市用户、县区用户
  if (currentRoles.includes('provincial')) {
    return [
      { label: "全部", value: "" },
      { label: "地市用户", value: "municipal" },
      { label: "县区用户", value: "county" },
    ];
  }

  // 市级用户：可以看到本市区、县级用户
  if (currentRoles.includes('municipal')) {
    return [
      { label: "全部", value: "" },
      { label: "本市区", value: "municipal" },
      { label: "县级用户", value: "county" },
    ];
  }

  // 县区级用户：无用户类型选项
  return [];
};

// 用户类型选项
const userTypeOptions = computed(() => getUserTypeOptions());

// 是否显示用户类型选择
const showUserTypeSelect = computed(() => {
  const currentRoles = userStore.roles;
  return currentRoles.includes('provincial') || currentRoles.includes('municipal');
});

// 搜索功能
const handleSearch = async () => {
  try {
    console.log("搜索参数:", searchForm.value);
    // 构建查询参数
    const params = {
      userType: searchForm.value.userType,
      keyword: searchForm.value.searchKeyword,
      page: pagination.value.currentPage,
      pageSize: pagination.value.pageSize,
    };

    // 调用API获取用户列表
    // const { data } = await getUserListApi(params);
    // tableData.value = data.list;
    // pagination.value.total = data.total;

    ElMessage.success("搜索完成");
  } catch (error) {
    ElMessage.error("搜索失败");
    console.error("搜索错误:", error);
  }
};

// 重置功能
const handleReset = () => {
  searchForm.value = {
    userType: "",
    searchKeyword: "",
  };
  console.log("表单已重置");
  // 重置后重新搜索
  handleSearch();
};

// 表格数据
const tableData = ref([
  {
    id: "10001",
    name: "丁汉雄",
    phone: "15257954678",
    role: "地市用户",
    city: "南京市",
    district: "鼓楼区",
    unit: "鼓楼区教育局",
  },
  {
    id: "10002",
    name: "卢木仲",
    phone: "13991955044",
    role: "县区用户",
    city: "南京市",
    district: "鼓楼区",
    unit: "鼓楼区教育局",
  },
  {
    id: "10003",
    name: "谢彦文",
    phone: "13112206029",
    role: "地市用户",
    city: "南京市",
    district: "鼓楼区",
    unit: "鼓楼区教育局",
  },
  {
    id: "10004",
    name: "王美珠",
    phone: "13488998888",
    role: "地市用户",
    city: "南京市",
    district: "鼓楼区",
    unit: "鼓楼区教育局",
  },
  {
    id: "10005",
    name: "李雅雅",
    phone: "13936729999",
    role: "地市用户",
    city: "南京市",
    district: "鼓楼区",
    unit: "鼓楼区教育局",
  },
  {
    id: "10006",
    name: "吴韵如",
    phone: "15205211783",
    role: "地市用户",
    city: "南京市",
    district: "鼓楼区",
    unit: "鼓楼区教育局",
  },
  {
    id: "10007",
    name: "王林萍",
    phone: "13592357387",
    role: "地市用户",
    city: "南京市",
    district: "鼓楼区",
    unit: "鼓楼区教育局",
  },
  {
    id: "10008",
    name: "郑昌梦",
    phone: "18268559855",
    role: "地市用户",
    city: "南京市",
    district: "鼓楼区",
    unit: "鼓楼区教育局",
  },
  {
    id: "10009",
    name: "曹敏倩",
    phone: "18212078564",
    role: "地市用户",
    city: "南京市",
    district: "鼓楼区",
    unit: "鼓楼区教育局",
  },
  {
    id: "10010",
    name: "侯怡芳",
    phone: "15576592483",
    role: "县区用户",
    city: "南京市",
    district: "鼓楼区",
    unit: "鼓楼区教育局",
  },
]);

// 分页数据
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 50,
});

// 分页变化处理
const handleCurrentChange = (page) => {
  pagination.value.currentPage = page;
  console.log("当前页:", page);
};

// 刷新数据
const handleRefresh = () => {
  console.log("刷新数据");
  handleSearch();
};

// 弹窗相关状态
const dialogVisible = ref(false);
const dialogTitle = ref('');
const isEdit = ref(false);

// 用户表单数据
const userForm = ref({
  name: '',
  phone: '',
  role: '',
  city: '',
  district: '',
  unit: ''
});

// 用户表单验证规则
const userFormRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色修改', trigger: 'change' }
  ],
  city: [
    { required: true, message: '请选择所属市区', trigger: 'change' }
  ],
  unit: [
    { required: true, message: '请选择所属单位', trigger: 'change' }
  ]
};

// 角色选项
const roleOptions = [
  { label: '地市用户', value: 'municipal' },
  { label: '县区用户', value: 'county' },
  { label: '省级用户', value: 'provincial' }
];

// 市区选项
const cityOptions = [
  { label: '南京市', value: 'nanjing' },
  { label: '苏州市', value: 'suzhou' },
  { label: '无锡市', value: 'wuxi' },
  { label: '常州市', value: 'changzhou' },
  { label: '镇江市', value: 'zhenjiang' },
  { label: '扬州市', value: 'yangzhou' },
  { label: '泰州市', value: 'taizhou' },
  { label: '南通市', value: 'nantong' },
  { label: '盐城市', value: 'yancheng' },
  { label: '淮安市', value: 'huaian' },
  { label: '宿迁市', value: 'suqian' },
  { label: '连云港市', value: 'lianyungang' },
  { label: '徐州市', value: 'xuzhou' }
];

// 单位选项（根据选择的市区动态变化）
const unitOptions = computed(() => {
  const selectedCity = userForm.value.city;
  if (!selectedCity) return [];

  // 这里可以根据实际需求配置不同市区的单位选项
  const unitMap = {
    'nanjing': [
      { label: '鼓楼区教育局', value: 'gulou_education' },
      { label: '玄武区教育局', value: 'xuanwu_education' },
      { label: '建邺区教育局', value: 'jianye_education' },
      { label: '秦淮区教育局', value: 'qinhuai_education' }
    ],
    'suzhou': [
      { label: '姑苏区教育局', value: 'gusu_education' },
      { label: '工业园区教育局', value: 'sip_education' },
      { label: '高新区教育局', value: 'snd_education' }
    ]
    // 可以继续添加其他市区的单位选项
  };

  return unitMap[selectedCity] || [
    { label: `${cityOptions.find(c => c.value === selectedCity)?.label}教育局`, value: `${selectedCity}_education` }
  ];
});

// 表单引用
const userFormRef = ref();

// 新增用户
const handleAddUser = () => {
  dialogTitle.value = '新增用户';
  isEdit.value = false;
  userForm.value = {
    name: '',
    phone: '',
    role: '',
    city: '',
    district: '',
    unit: ''
  };
  dialogVisible.value = true;
};

// 导出数据
const handleExport = () => {
  console.log("导出数据");
  ElMessage.success("数据导出成功");
};

// 编辑用户
const handleEditUser = (row) => {
  dialogTitle.value = '编辑用户';
  isEdit.value = true;
  userForm.value = {
    id: row.id,
    name: row.name,
    phone: row.phone,
    role: getRoleValue(row.role),
    city: getCityValue(row.city),
    district: row.district,
    unit: getUnitValue(row.unit)
  };
  dialogVisible.value = true;
};

// 获取角色值
const getRoleValue = (roleLabel) => {
  const role = roleOptions.find(r => r.label === roleLabel);
  return role ? role.value : '';
};

// 获取市区值
const getCityValue = (cityLabel) => {
  const city = cityOptions.find(c => c.label === cityLabel);
  return city ? city.value : '';
};

// 获取单位值
const getUnitValue = (unitLabel) => {
  // 这里可以根据实际情况实现单位值的获取逻辑
  return unitLabel;
};

// 关闭弹窗
const handleCloseDialog = () => {
  dialogVisible.value = false;
  userFormRef.value?.resetFields();
};

// 提交表单
const handleSubmitForm = async () => {
  try {
    await userFormRef.value.validate();

    if (isEdit.value) {
      // 编辑用户逻辑
      console.log('编辑用户:', userForm.value);
      ElMessage.success('用户信息修改成功');
    } else {
      // 新增用户逻辑
      console.log('新增用户:', userForm.value);
      ElMessage.success('用户添加成功');
    }

    handleCloseDialog();
    // 刷新列表
    handleSearch();
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

// 操作处理
const handleOperation = async (row, action) => {
  if (action === 'edit') {
    handleEditUser(row);
  } else if (action === 'reset-password') {
    try {
      await ElMessageBox.confirm(
        `确定要重置用户 ${row.name} 的密码吗？`,
        '重置密码确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      );

      // 调用重置密码API
      // await resetUserPasswordApi(row.id);
      ElMessage.success(`用户 ${row.name} 的密码已重置`);
      console.log('重置密码成功:', row);
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('重置密码失败');
        console.error('重置密码错误:', error);
      }
    }
  }
};

// 获取角色CSS类名
const getRoleClass = (role) => {
  const classMap = {
    地市用户: "sheng-role",
    县区用户: "shi-role",
    省级用户: "qu-role",
  };
  return classMap[role] || "role-tag";
};
</script>

<template>
  <div class="complaint-distribution app-container">
    <TopTitle title="用户管理"></TopTitle>
    <section class="search-form px-[24px] py-[16px] rounded-[2px] bg-[#fff]">
      <el-form :model="searchForm" class="search-form-3" label-position="top">
        <!-- 左侧表单项 -->
        <div class="form-left">
          <!-- 用户类型选择 -->
          <el-form-item v-if="showUserTypeSelect" label="用户类型">
            <el-select v-model="searchForm.userType" placeholder="全部">
              <el-option
                v-for="item in userTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <!-- 手机号/姓名搜索 -->
          <el-form-item label="手机号/姓名搜索">
            <el-input
              v-model="searchForm.searchKeyword"
              placeholder="请输入手机号或姓名"
              style="width: 240px"
            />
          </el-form-item>
        </div>

        <!-- 右侧按钮组 -->
        <div class="form-right">
          <el-form-item class="hidden-label" label="按钮">
            <el-button class="common-button-3" @click="handleSearch">
              <template #icon>
                <el-icon>
                  <Search />
                </el-icon>
              </template>
              查询
            </el-button>
            <el-button class="reset-button" @click="handleReset">
              <template #icon>
                <el-icon><RefreshRight /></el-icon>
              </template>
              重置
            </el-button>
          </el-form-item>
        </div>
      </el-form>
    </section>
    <section
      class="table-container mt-[16px] px-[24px] py-[16px] rounded-[2px] bg-[#fff]"
    >
      <!-- 操作按钮 -->
      <div class="mb-[16px] flex justify-between">
        <el-button class="common-button-4" @click="handleRefresh">
          <template #icon>
            <el-icon><RefreshRight /></el-icon>
          </template>
          刷新
        </el-button>
        <div class="flex gap-[16px]">
          <el-button class="common-button-3" @click="handleAddUser">
            <template #icon>
              <el-icon><Plus /></el-icon>
            </template>
            新增用户
          </el-button>
          <el-button class="common-button-3" @click="handleExport">
            <template #icon>
              <img
                src="@/assets/images/common/export.png"
                alt=""
                width="16"
                height="16"
              />
            </template>
            导出数据
          </el-button>
        </div>
      </div>

      <!-- 表格 -->
      <el-table
        :data="tableData"
        style="width: 100%"
        class="custom-table"
        stripe
      >
        <!-- 序号 -->
        <el-table-column
          prop="id"
          label="序号"
          width="80"
          align="center"
        />

        <!-- 姓名 -->
        <el-table-column
          prop="name"
          label="姓名"
          min-width="120"
          align="center"
        />

        <!-- 手机号 -->
        <el-table-column
          prop="phone"
          label="手机号"
          min-width="140"
          align="center"
        />

        <!-- 角色 -->
        <el-table-column label="角色" min-width="120" align="center">
          <template #default="{ row }">
            <div :class="['role-tag', getRoleClass(row.role)]">
              {{ row.role }}
            </div>
          </template>
        </el-table-column>

        <!-- 市区 -->
        <el-table-column
          prop="city"
          label="市区"
          min-width="120"
          align="center"
        />

        <!-- 县区 -->
        <el-table-column
          prop="district"
          label="县区"
          min-width="120"
          align="center"
        />

        <!-- 单位 -->
        <el-table-column
          prop="unit"
          label="单位"
          min-width="200"
          align="center"
        />

        <!-- 操作列 -->
        <el-table-column label="操作" width="200" align="center">
          <template #default="{ row }">
            <div class="flex-center-center gap-[16px]">
              <div
                class="pointer blue2"
                @click="handleOperation(row, 'edit')"
              >
                编辑
              </div>
              <div
                class="pointer red1"
                @click="handleOperation(row, 'reset-password')"
              >
                重置密码
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="mt-[16px] flex justify-end">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          :total="pagination.total"
          layout="total, prev, pager, next"
          @current-change="handleCurrentChange"
        />
      </div>
    </section>

    <!-- 用户弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="480px"
      class="custom-dialog"
      :before-close="handleCloseDialog"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userFormRules"
        label-position="top"
        class="user-form"
      >
        <el-form-item label="姓名" prop="name" required>
          <el-input
            v-model="userForm.name"
            placeholder="请输入姓名"
            maxlength="20"
          />
        </el-form-item>

        <el-form-item label="手机号" prop="phone" required>
          <el-input
            v-model="userForm.phone"
            placeholder="请输入手机号"
            maxlength="11"
          />
        </el-form-item>

        <el-form-item label="角色修改" prop="role" required>
          <el-select
            v-model="userForm.role"
            placeholder="请选择角色"
            style="width: 100%"
          >
            <el-option
              v-for="item in roleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="所属市区" prop="city" required>
          <el-select
            v-model="userForm.city"
            placeholder="请选择所属市区"
            style="width: 100%"
            @change="userForm.unit = ''"
          >
            <el-option
              v-for="item in cityOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="所属单位" prop="unit" required>
          <el-select
            v-model="userForm.unit"
            placeholder="请选择所属单位"
            style="width: 100%"
            :disabled="!userForm.city"
          >
            <el-option
              v-for="item in unitOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseDialog">取消</el-button>
          <el-button type="primary" @click="handleSubmitForm">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.hidden-label {
  :deep(.el-form-item__label) {
    display: none;
  }
}

.whitespace-pre-line {
  white-space: pre-line;
  font-size: 16px;
  line-height: 24px;
}

// 角色标签样式
.role-tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;

  &.role-municipal {
    background-color: #e6f7ff;
    color: #1890ff;
    border: 1px solid #91d5ff;
  }

  &.role-county {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
  }

  &.role-provincial {
    background-color: #fff2e8;
    color: #fa8c16;
    border: 1px solid #ffd591;
  }

  &.role-default {
    background-color: #f5f5f5;
    color: #666;
    border: 1px solid #d9d9d9;
  }
}

// 用户表单样式
.user-form {
  .el-form-item {
    margin-bottom: 20px;

    .el-form-item__label {
      color: var(--grey1);
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      margin-bottom: 8px;
    }

    .el-input__wrapper {
      height: 48px;
      font-size: 16px;
    }

    .el-select {
      .el-select__wrapper {
        height: 48px;
        font-size: 16px;
      }
    }
  }
}

</style>
