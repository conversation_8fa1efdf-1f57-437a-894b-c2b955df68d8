# 用户管理弹窗功能实现

## 功能概述

已成功为用户管理页面添加了新增用户和编辑用户的弹窗功能，完全按照设计图要求实现。

## 功能特点

### 1. 弹窗标题动态变化
- 点击"新增用户"按钮时，弹窗标题显示为"新增用户"
- 点击表格中的"编辑"按钮时，弹窗标题显示为"编辑用户"

### 2. 表单字段
- **姓名**：文本输入框，必填项，最大长度20字符
- **手机号**：文本输入框，必填项，支持手机号格式验证
- **角色修改**：下拉选择框，包含地市用户、县区用户、省级用户选项
- **所属市区**：下拉选择框，包含江苏省13个地级市选项
- **所属单位**：下拉选择框，根据选择的市区动态显示对应的教育局选项

### 3. 表单验证
- 姓名：必填验证
- 手机号：必填验证 + 正则表达式格式验证（1开头的11位数字）
- 角色修改：必填验证
- 所属市区：必填验证
- 所属单位：必填验证

### 4. 交互功能
- 选择市区后，所属单位选项会自动更新
- 表单验证实时反馈
- 支持取消和确认操作
- 提交成功后自动关闭弹窗并重置表单

## 技术实现

### 1. 弹窗状态管理
```javascript
// 弹窗相关状态
const dialogVisible = ref(false);
const dialogTitle = ref('');
const isEdit = ref(false);
```

### 2. 表单数据结构
```javascript
const userForm = ref({
  name: '',        // 姓名
  phone: '',       // 手机号
  role: '',        // 角色
  city: '',        // 市区
  district: '',    // 区县
  unit: ''         // 单位
});
```

### 3. 动态选项配置
```javascript
// 角色选项
const roleOptions = [
  { label: '地市用户', value: 'municipal' },
  { label: '县区用户', value: 'county' },
  { label: '省级用户', value: 'provincial' }
];

// 市区选项（江苏省13个地级市）
const cityOptions = [
  { label: '南京市', value: 'nanjing' },
  { label: '苏州市', value: 'suzhou' },
  // ... 其他市区
];

// 单位选项（根据选择的市区动态变化）
const unitOptions = computed(() => {
  // 根据选择的市区返回对应的教育局选项
});
```

### 4. 核心功能函数
```javascript
// 新增用户
const handleAddUser = () => {
  dialogTitle.value = '新增用户';
  isEdit.value = false;
  // 重置表单数据
  dialogVisible.value = true;
};

// 编辑用户
const handleEditUser = (row) => {
  dialogTitle.value = '编辑用户';
  isEdit.value = true;
  // 填充表单数据
  dialogVisible.value = true;
};

// 提交表单
const handleSubmitForm = async () => {
  await userFormRef.value.validate();
  // 根据isEdit判断是新增还是编辑
  // 提交成功后关闭弹窗
};
```

## 样式设计

### 1. 弹窗样式
- 使用全局类 `custom-dialog`
- 宽度设置为 600px
- 标题字体大小 18px，字重 600
- 内边距 20px

### 2. 表单样式
- 标签位置设置为顶部对齐
- 表单项间距 20px
- 输入框高度 48px
- 字体大小 16px
- 标签颜色使用主题色 `--grey1`

### 3. 按钮样式
- 底部右对齐布局
- 按钮间距 16px
- 最小宽度 80px，高度 40px

## 文件修改记录

### 主要修改文件
- `src/views/admin/system/user/index.vue`：添加用户弹窗功能

### 新增功能
1. 弹窗状态管理
2. 用户表单数据结构
3. 表单验证规则配置
4. 角色和地区选项配置
5. 新增用户功能
6. 编辑用户功能
7. 表单提交逻辑
8. 弹窗样式定义

### 兼容性
- 保持与现有功能的完全兼容
- 不影响其他页面和组件
- 使用项目现有的样式规范和组件库

## 演示页面

已创建独立的演示页面 `user-dialog-demo.html`，可以直接在浏览器中打开查看弹窗功能的完整效果，包括：
- 新增用户弹窗
- 编辑用户弹窗
- 表单验证
- 动态选项联动
- 样式展示

## 使用方式

### 1. 新增用户
1. 点击页面右上角的"新增用户"按钮
2. 弹窗标题显示"新增用户"
3. 填写所有必填字段
4. 点击"确认"按钮提交

### 2. 编辑用户
1. 在用户列表中点击某行的"编辑"按钮
2. 弹窗标题显示"编辑用户"
3. 表单自动填充该用户的现有信息
4. 修改需要更新的字段
5. 点击"确认"按钮提交

### 3. 表单验证
- 所有必填字段都会进行验证
- 手机号会进行格式验证
- 验证失败时会显示错误提示
- 只有通过验证才能提交

## 后续扩展

1. **API集成**：可以很容易地集成实际的用户管理API
2. **权限控制**：可以根据用户角色控制字段的可编辑性
3. **批量操作**：可以扩展支持批量新增或编辑用户
4. **高级验证**：可以添加更复杂的业务逻辑验证
5. **文件上传**：可以添加用户头像上传功能

## 技术特点

1. **响应式设计**：表单在不同屏幕尺寸下都有良好的显示效果
2. **用户体验**：流畅的交互动画和及时的反馈提示
3. **代码复用**：新增和编辑功能共用同一个弹窗组件
4. **数据验证**：完善的前端验证机制
5. **样式统一**：遵循项目整体的设计规范
