<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户弹窗演示</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        .demo-buttons {
            display: flex;
            gap: 16px;
            margin-bottom: 20px;
        }
        
        /* 自定义弹窗样式 */
        .custom-dialog {
            padding: 20px;
            padding-bottom: 32px;
        }
        
        .custom-dialog .el-dialog__header > .el-dialog__title {
            color: #2b2c33;
            font-weight: 600;
            font-size: 18px;
        }
        
        .custom-dialog .el-dialog__headerbtn {
            width: 50px;
            height: 55px;
            font-size: 16px;
        }
        
        .custom-dialog .el-dialog__body {
            padding-top: 10px;
        }
        
        .user-form .el-form-item {
            margin-bottom: 20px;
        }
        
        .user-form .el-form-item__label {
            color: #2b2c33;
            font-size: 16px;
            font-weight: 600;
            line-height: 24px;
            margin-bottom: 8px;
        }
        
        .user-form .el-input__wrapper {
            height: 48px;
            font-size: 16px;
        }
        
        .user-form .el-select .el-select__wrapper {
            height: 48px;
            font-size: 16px;
        }
        
        .dialog-footer {
            display: flex;
            justify-content: flex-end;
            gap: 16px;
        }
        
        .dialog-footer .el-button {
            min-width: 80px;
            height: 40px;
        }
        
        .sample-table {
            margin-top: 20px;
        }
        
        .edit-btn {
            color: #239DDE;
            cursor: pointer;
        }
        
        .edit-btn:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="demo-container">
            <h2>用户管理弹窗演示</h2>
            
            <div class="demo-buttons">
                <el-button type="primary" @click="handleAddUser">新增用户</el-button>
                <el-button @click="handleEditUser">编辑用户示例</el-button>
            </div>
            
            <!-- 示例表格 -->
            <div class="sample-table">
                <h3>用户列表示例</h3>
                <el-table :data="sampleUsers" style="width: 100%" stripe>
                    <el-table-column prop="name" label="姓名" width="120" align="center" />
                    <el-table-column prop="phone" label="手机号" width="140" align="center" />
                    <el-table-column prop="role" label="角色" width="120" align="center" />
                    <el-table-column prop="city" label="市区" width="120" align="center" />
                    <el-table-column prop="unit" label="单位" min-width="200" align="center" />
                    <el-table-column label="操作" width="100" align="center">
                        <template #default="{ row }">
                            <span class="edit-btn" @click="handleEditUserFromTable(row)">编辑</span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            
            <!-- 用户弹窗 -->
            <el-dialog
                v-model="dialogVisible"
                :title="dialogTitle"
                width="600px"
                class="custom-dialog"
                :before-close="handleCloseDialog"
            >
                <el-form
                    ref="userFormRef"
                    :model="userForm"
                    :rules="userFormRules"
                    label-position="top"
                    class="user-form"
                >
                    <el-form-item label="姓名" prop="name" required>
                        <el-input
                            v-model="userForm.name"
                            placeholder="请输入姓名"
                            maxlength="20"
                        />
                    </el-form-item>

                    <el-form-item label="手机号" prop="phone" required>
                        <el-input
                            v-model="userForm.phone"
                            placeholder="请输入手机号"
                            maxlength="11"
                        />
                    </el-form-item>

                    <el-form-item label="角色修改" prop="role" required>
                        <el-select
                            v-model="userForm.role"
                            placeholder="请选择角色"
                            style="width: 100%"
                        >
                            <el-option
                                v-for="item in roleOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item label="所属市区" prop="city" required>
                        <el-select
                            v-model="userForm.city"
                            placeholder="请选择所属市区"
                            style="width: 100%"
                            @change="userForm.unit = ''"
                        >
                            <el-option
                                v-for="item in cityOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item label="所属单位" prop="unit" required>
                        <el-select
                            v-model="userForm.unit"
                            placeholder="请选择所属单位"
                            style="width: 100%"
                            :disabled="!userForm.city"
                        >
                            <el-option
                                v-for="item in unitOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                </el-form>

                <template #footer>
                    <div class="dialog-footer">
                        <el-button @click="handleCloseDialog">取消</el-button>
                        <el-button type="primary" @click="handleSubmitForm">确认</el-button>
                    </div>
                </template>
            </el-dialog>
        </div>
    </div>

    <script>
        const { createApp, ref, computed } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            setup() {
                // 弹窗相关状态
                const dialogVisible = ref(false);
                const dialogTitle = ref('');
                const isEdit = ref(false);

                // 用户表单数据
                const userForm = ref({
                    name: '',
                    phone: '',
                    role: '',
                    city: '',
                    unit: ''
                });

                // 用户表单验证规则
                const userFormRules = {
                    name: [
                        { required: true, message: '请输入姓名', trigger: 'blur' }
                    ],
                    phone: [
                        { required: true, message: '请输入手机号', trigger: 'blur' },
                        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
                    ],
                    role: [
                        { required: true, message: '请选择角色修改', trigger: 'change' }
                    ],
                    city: [
                        { required: true, message: '请选择所属市区', trigger: 'change' }
                    ],
                    unit: [
                        { required: true, message: '请选择所属单位', trigger: 'change' }
                    ]
                };

                // 角色选项
                const roleOptions = [
                    { label: '地市用户', value: 'municipal' },
                    { label: '县区用户', value: 'county' },
                    { label: '省级用户', value: 'provincial' }
                ];

                // 市区选项
                const cityOptions = [
                    { label: '南京市', value: 'nanjing' },
                    { label: '苏州市', value: 'suzhou' },
                    { label: '无锡市', value: 'wuxi' },
                    { label: '常州市', value: 'changzhou' },
                    { label: '镇江市', value: 'zhenjiang' },
                    { label: '扬州市', value: 'yangzhou' },
                    { label: '泰州市', value: 'taizhou' },
                    { label: '南通市', value: 'nantong' },
                    { label: '盐城市', value: 'yancheng' },
                    { label: '淮安市', value: 'huaian' },
                    { label: '宿迁市', value: 'suqian' },
                    { label: '连云港市', value: 'lianyungang' },
                    { label: '徐州市', value: 'xuzhou' }
                ];

                // 单位选项（根据选择的市区动态变化）
                const unitOptions = computed(() => {
                    const selectedCity = userForm.value.city;
                    if (!selectedCity) return [];
                    
                    const unitMap = {
                        'nanjing': [
                            { label: '鼓楼区教育局', value: 'gulou_education' },
                            { label: '玄武区教育局', value: 'xuanwu_education' },
                            { label: '建邺区教育局', value: 'jianye_education' },
                            { label: '秦淮区教育局', value: 'qinhuai_education' }
                        ],
                        'suzhou': [
                            { label: '姑苏区教育局', value: 'gusu_education' },
                            { label: '工业园区教育局', value: 'sip_education' },
                            { label: '高新区教育局', value: 'snd_education' }
                        ]
                    };
                    
                    return unitMap[selectedCity] || [
                        { label: `${cityOptions.find(c => c.value === selectedCity)?.label}教育局`, value: `${selectedCity}_education` }
                    ];
                });

                // 示例用户数据
                const sampleUsers = ref([
                    {
                        id: 1,
                        name: '张三',
                        phone: '13812345678',
                        role: '地市用户',
                        city: '南京市',
                        unit: '鼓楼区教育局'
                    },
                    {
                        id: 2,
                        name: '李四',
                        phone: '13987654321',
                        role: '县区用户',
                        city: '苏州市',
                        unit: '姑苏区教育局'
                    }
                ]);

                // 表单引用
                const userFormRef = ref();

                // 新增用户
                const handleAddUser = () => {
                    dialogTitle.value = '新增用户';
                    isEdit.value = false;
                    userForm.value = {
                        name: '',
                        phone: '',
                        role: '',
                        city: '',
                        unit: ''
                    };
                    dialogVisible.value = true;
                };

                // 编辑用户示例
                const handleEditUser = () => {
                    dialogTitle.value = '编辑用户';
                    isEdit.value = true;
                    userForm.value = {
                        id: 1,
                        name: '张三',
                        phone: '13812345678',
                        role: 'municipal',
                        city: 'nanjing',
                        unit: 'gulou_education'
                    };
                    dialogVisible.value = true;
                };

                // 从表格编辑用户
                const handleEditUserFromTable = (row) => {
                    dialogTitle.value = '编辑用户';
                    isEdit.value = true;
                    
                    // 转换显示值为选项值
                    const roleValue = roleOptions.find(r => r.label === row.role)?.value || '';
                    const cityValue = cityOptions.find(c => c.label === row.city)?.value || '';
                    
                    userForm.value = {
                        id: row.id,
                        name: row.name,
                        phone: row.phone,
                        role: roleValue,
                        city: cityValue,
                        unit: 'gulou_education' // 这里可以根据实际情况设置
                    };
                    dialogVisible.value = true;
                };

                // 关闭弹窗
                const handleCloseDialog = () => {
                    dialogVisible.value = false;
                    userFormRef.value?.resetFields();
                };

                // 提交表单
                const handleSubmitForm = async () => {
                    try {
                        await userFormRef.value.validate();
                        
                        if (isEdit.value) {
                            ElMessage.success('用户信息修改成功');
                        } else {
                            ElMessage.success('用户添加成功');
                        }
                        
                        handleCloseDialog();
                    } catch (error) {
                        console.error('表单验证失败:', error);
                    }
                };

                return {
                    dialogVisible,
                    dialogTitle,
                    isEdit,
                    userForm,
                    userFormRules,
                    roleOptions,
                    cityOptions,
                    unitOptions,
                    sampleUsers,
                    userFormRef,
                    handleAddUser,
                    handleEditUser,
                    handleEditUserFromTable,
                    handleCloseDialog,
                    handleSubmitForm
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
